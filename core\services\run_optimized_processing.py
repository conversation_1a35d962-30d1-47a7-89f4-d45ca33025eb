"""
Run optimized processing with different modes.
This script allows you to process audio/video files with different optimization settings.
"""

import os
import sys
import time
import argparse
from datetime import timedelta

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def format_time(seconds):
    """Format seconds into a human-readable time string."""
    return str(timedelta(seconds=int(seconds)))

def run_processing(input_file, title, description, mode="auto"):
    """
    Run processing with specified mode.

    Args:
        input_file (str): Path to input file
        title (str): Title of the meeting
        description (str): Description or attendance information
        mode (str): Processing mode ('gpu', 'cpu', or 'auto')

    Returns:
        dict: Processing results
    """
    # Import GPU configuration
    try:
        # Add utils directory to path for gpu_config
        sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'utils'))
        from gpu_config import setup_environment
        has_gpu_config = True
    except ImportError:
        has_gpu_config = False
        print("GPU configuration not available, using default settings")

    # Set up environment based on mode
    if has_gpu_config:
        if mode == "gpu":
            print("Using GPU-only mode")
            setup_environment(force_cpu=False, force_gpu=True)
        elif mode == "cpu":
            print("Using CPU-only mode")
            setup_environment(force_cpu=True)
        else:  # auto mode
            print("Using automatic device selection mode")
            setup_environment(force_cpu=False, force_gpu=False)

    # Import processing module
    from fast_process import process_file

    # Process the file
    start_time = time.time()
    result = process_file(input_file, title, description, "user")
    processing_time = time.time() - start_time

    # Add processing mode to result
    result["processing_mode"] = mode
    result["processing_time"] = processing_time
    result["processing_time_formatted"] = format_time(processing_time)

    return result

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Run optimized processing with different modes")
    parser.add_argument("input_file", help="Path to input audio/video file")
    parser.add_argument("--title", default="Meeting", help="Title of the meeting")
    parser.add_argument("--description", default="", help="Description or attendance information")
    parser.add_argument("--mode", choices=["gpu", "cpu", "auto"], default="auto",
                        help="Processing mode: gpu (GPU only), cpu (CPU only), or auto (automatic selection)")

    args = parser.parse_args()

    # Verify file exists
    if not os.path.exists(args.input_file):
        print(f"Error: File not found: {args.input_file}")
        return 1

    # Run processing
    result = run_processing(args.input_file, args.title, args.description, args.mode)

    # Print results
    print("\n=== Processing Results ===\n")
    print(f"File: {args.input_file}")
    print(f"Mode: {result['processing_mode']}")
    print(f"Status: {result['status']}")

    if result['status'] == "success":
        print(f"Processing time: {result['processing_time_formatted']} ({result['processing_time']:.2f} seconds)")
        print(f"Transcript file: {result['data']['transcript_file']}")
        print(f"Minutes file: {result['data']['minutes_file']}")
        print(f"Speakers detected: {len(result['data']['speakers'])}")
    else:
        print(f"Error: {result.get('error', 'Unknown error')}")

    return 0

if __name__ == "__main__":
    sys.exit(main())
