#!/usr/bin/env python3
"""
Simple Minutes Generator
A lightweight alternative to Phase2V2 for generating meeting minutes when the full pipeline fails.
This script processes transcripts and generates basic meeting minutes.
"""

import os
import sys
import re
import argparse
from datetime import datetime
from collections import defaultdict, Counter

def extract_speakers(transcript_lines):
    """Extract unique speakers from transcript lines."""
    speakers = set()
    for line in transcript_lines:
        if ':' in line:
            speaker = line.split(':', 1)[0].strip()
            if speaker and speaker != 'Unknown':
                speakers.add(speaker)
    return sorted(list(speakers))

def extract_key_topics(transcript_lines):
    """Extract key topics and themes from the transcript."""
    # Common academic/meeting keywords
    keywords = {
        'curriculum': ['curriculum', 'course', 'program', 'academic'],
        'evaluation': ['evaluation', 'assessment', 'review', 'feedback'],
        'examination': ['examination', 'exam', 'test', 'final'],
        'graduation': ['graduation', 'graduate', 'graduating'],
        'accreditation': ['accreditation', 'accredited'],
        'research': ['research', 'study', 'investigation'],
        'budget': ['budget', 'fund', 'money', 'cost', 'expense'],
        'faculty': ['faculty', 'teacher', 'instructor', 'professor'],
        'student': ['student', 'learner', 'pupil'],
        'meeting': ['meeting', 'discussion', 'agenda'],
        'requirements': ['requirement', 'needed', 'necessary'],
        'preparation': ['preparation', 'prepare', 'ready'],
        'semester': ['semester', 'term', 'academic year'],
        'ojt': ['ojt', 'internship', 'training'],
        'educational tour': ['educational tour', 'field trip', 'tour'],
        'igp': ['igp', 'project']
    }
    
    topic_counts = defaultdict(int)
    all_text = ' '.join(transcript_lines).lower()
    
    for topic, terms in keywords.items():
        for term in terms:
            topic_counts[topic] += all_text.count(term.lower())
    
    # Return topics with significant mentions (at least 2 occurrences)
    significant_topics = {topic: count for topic, count in topic_counts.items() if count >= 2}
    return dict(sorted(significant_topics.items(), key=lambda x: x[1], reverse=True))

def extract_action_items(transcript_lines):
    """Extract potential action items from the transcript."""
    action_indicators = [
        'need to', 'should', 'must', 'will', 'prepare', 'submit', 'complete',
        'deadline', 'due', 'requirement', 'assign', 'responsible', 'follow up'
    ]
    
    action_items = []
    for line in transcript_lines:
        line_lower = line.lower()
        if any(indicator in line_lower for indicator in action_indicators):
            # Clean up the line and add it as a potential action item
            clean_line = re.sub(r'^[^:]*:', '', line).strip()
            if len(clean_line) > 20:  # Only include substantial content
                action_items.append(clean_line)
    
    return action_items[:10]  # Limit to top 10 action items

def extract_dates_and_deadlines(transcript_lines):
    """Extract dates and deadlines mentioned in the transcript."""
    date_patterns = [
        r'\b(?:january|february|march|april|may|june|july|august|september|october|november|december)\s+\d{1,2}\b',
        r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b',
        r'\bmay\s+\d{1,2}\b',
        r'\bjune\s+\d{1,2}\b',
        r'\bapril\s+\d{1,2}\b'
    ]
    
    dates = []
    all_text = ' '.join(transcript_lines).lower()
    
    for pattern in date_patterns:
        matches = re.findall(pattern, all_text, re.IGNORECASE)
        dates.extend(matches)
    
    return list(set(dates))  # Remove duplicates

def generate_simple_minutes(transcript_file_path, title="Meeting Minutes", meeting_date=None):
    """Generate simple meeting minutes from a transcript file."""
    
    # Read transcript
    with open(transcript_file_path, 'r', encoding='utf-8') as f:
        transcript_lines = [line.strip() for line in f.readlines() if line.strip()]
    
    # Extract information
    speakers = extract_speakers(transcript_lines)
    topics = extract_key_topics(transcript_lines)
    action_items = extract_action_items(transcript_lines)
    dates = extract_dates_and_deadlines(transcript_lines)
    
    # Generate minutes
    if not meeting_date:
        meeting_date = datetime.now().strftime("%B %d, %Y")
    
    minutes = f"""# {title}

**Date:** {meeting_date}
**Attendees:** {len(speakers)} participants

## Present Faculty Attendees

{chr(10).join(f"- {speaker}" for speaker in speakers)}

## Meeting Overview

This meeting covered several important topics related to academic operations, curriculum development, and administrative matters. The discussion included {len(topics)} main topic areas with active participation from {len(speakers)} faculty members.

## Key Discussion Topics

"""
    
    # Add topics section
    for i, (topic, count) in enumerate(list(topics.items())[:8], 1):
        topic_title = topic.replace('_', ' ').title()
        minutes += f"### {i}. {topic_title}\n\n"
        minutes += f"This topic was discussed extensively throughout the meeting (mentioned {count} times).\n\n"
    
    # Add action items if any were found
    if action_items:
        minutes += "## Action Items\n\n"
        for i, item in enumerate(action_items[:5], 1):
            # Clean up the action item
            clean_item = re.sub(r'\s+', ' ', item).strip()
            if len(clean_item) > 30:  # Only include substantial items
                minutes += f"{i}. {clean_item}\n\n"
    
    # Add important dates if found
    if dates:
        minutes += "## Important Dates\n\n"
        for date in dates[:5]:
            minutes += f"- {date}\n"
        minutes += "\n"
    
    # Add closing
    minutes += """## Meeting Conclusion

The meeting concluded with all participants having the opportunity to contribute to the discussions. Follow-up actions and next steps were identified for implementation.

---

*These minutes were generated from the meeting transcript and provide a summary of the key points discussed.*
"""
    
    return minutes

def main():
    """Main function to process transcript and generate minutes."""
    parser = argparse.ArgumentParser(description="Generate simple meeting minutes from transcript")
    parser.add_argument("transcript_file", help="Path to the transcript file")
    parser.add_argument("--title", default="Meeting Minutes", help="Title for the meeting")
    parser.add_argument("--date", help="Meeting date (default: current date)")
    parser.add_argument("--output", help="Output file path (default: same directory as transcript)")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.transcript_file):
        print(f"Error: Transcript file not found: {args.transcript_file}")
        return 1
    
    try:
        # Generate minutes
        minutes = generate_simple_minutes(args.transcript_file, args.title, args.date)
        
        # Determine output file
        if args.output:
            output_file = args.output
        else:
            base_name = os.path.splitext(args.transcript_file)[0]
            output_file = f"{base_name}_minutes.md"
        
        # Write minutes to file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(minutes)
        
        print(f"Minutes generated successfully: {output_file}")
        return 0
        
    except Exception as e:
        print(f"Error generating minutes: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
