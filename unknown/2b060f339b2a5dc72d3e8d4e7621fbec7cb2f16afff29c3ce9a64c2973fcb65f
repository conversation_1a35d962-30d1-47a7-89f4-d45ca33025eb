import os
import sys
import json
import time
import logging
import traceback
from datetime import datetime
import tempfile
import subprocess

def sanitize_json_data(data):
    """
    Recursively sanitize data to ensure it can be safely encoded as JSON.
    Handles problematic characters and non-serializable objects.

    Args:
        data: The data to sanitize (can be dict, list, str, or other types)

    Returns:
        The sanitized data that can be safely JSON-encoded
    """
    if isinstance(data, dict):
        return {k: sanitize_json_data(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [sanitize_json_data(item) for item in data]
    elif isinstance(data, str):
        # Replace problematic characters with safe alternatives
        # This handles Unicode characters that might cause encoding issues
        return data.encode('ascii', 'replace').decode('ascii')
    elif isinstance(data, (int, float, bool, type(None))):
        # These types are JSON-serializable as is
        return data
    else:
        # Convert other types to strings
        return str(data)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filename='direct_process.log'
)

# Import Pipeline Phase 1
sys.path.append(os.path.join(os.path.dirname(__file__), 'Pipeline Phase1'))
from PHASE1 import AcademicTranscriptionPipeline

# Import Pipeline Phase 2
sys.path.append(os.path.join(os.path.dirname(__file__), 'Pipeline Phase2'))

# Enable GPU usage with memory management
os.environ['CUDA_VISIBLE_DEVICES'] = '0'  # Use the first GPU
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:512'  # Increased from 128

# Set memory limits for TensorFlow
os.environ['TF_FORCE_GPU_ALLOW_GROWTH'] = 'true'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

# Set PyTorch parallelism settings - use more CPU cores
cpu_count = os.cpu_count()
optimal_threads = max(8, min(16, cpu_count))  # Use between 8-16 threads
os.environ['OMP_NUM_THREADS'] = str(optimal_threads)
os.environ['MKL_NUM_THREADS'] = str(optimal_threads)

# Increase system pagefile usage limit
import ctypes
ctypes.windll.kernel32.SetProcessWorkingSetSize(-1, -1, -1)

# Now import the module after setting environment variables
# Try to import from Phase2V2 first, with fallback to Phase2 if there's an error
try:
    # Try to import from Phase2V2 (improved version)
    print("Attempting to use Phase2V2 for improved performance...")
    from Phase2V2 import process_meeting_transcript_enhanced as phase2v2_process

    # Create a wrapper function to adapt Phase2V2's function to work with progress_callback
    def process_meeting_transcript_enhanced(transcript, include_executive_summary=True, progress_callback=None):
        """
        Wrapper function to adapt Phase2V2's process_meeting_transcript_enhanced to work with progress_callback.

        Args:
            transcript (str): The meeting transcript to process
            include_executive_summary (bool): Whether to include an executive summary
            progress_callback (callable): Optional callback function for progress updates

        Returns:
            str: Formatted meeting minutes
        """
        # Call Phase2V2's function with the appropriate parameters
        # Set enhance_transcript to False to avoid potential memory issues
        print("Using Phase2V2 for processing...")
        return phase2v2_process(transcript, include_executive_summary=include_executive_summary, enhance_transcript=False)

    print("Successfully loaded Phase2V2")

except Exception as e:
    # If there's an error with Phase2V2, fall back to the original Phase2
    print(f"Error loading Phase2V2: {str(e)}")
    print("Falling back to original Phase2...")
    from PHASE2 import process_meeting_transcript_enhanced
    print("Successfully loaded original Phase2 as fallback")

def send_progress(stage, progress, details=None):
    """Send progress updates to stderr in JSON format for Node.js to parse"""
    # Sanitize details to ensure it's ASCII-compatible
    if details:
        # Replace problematic Unicode characters with ASCII equivalents
        import unicodedata
        # First try to normalize to closest ASCII equivalent
        normalized_details = unicodedata.normalize('NFKD', details)
        # Then replace any remaining non-ASCII with '?'
        ascii_details = normalized_details.encode('ascii', 'replace').decode('ascii')
        details = ascii_details

    progress_data = {
        "type": "progress",
        "stage": stage,
        "progress": progress
    }
    if details:
        progress_data["details"] = details

    # Write progress data to stderr for Node.js to capture
    sys.stderr.write(json.dumps(progress_data, ensure_ascii=True) + "\n")
    sys.stderr.flush()

    # Also log to stdout for debugging
    if details:
        print(f"{stage}: {details}", file=sys.stdout)
    else:
        print(f"{stage}: {progress}%", file=sys.stdout)
    sys.stdout.flush()

def convert_to_wav(input_file):
    """Convert input file to WAV format if needed"""
    file_ext = os.path.splitext(input_file)[1].lower()

    # If already a WAV file, return the original
    if file_ext == '.wav':
        return input_file

    # Create output filename
    output_file = os.path.join(
        tempfile.gettempdir(),
        f"converted_{int(time.time())}.wav"
    )

    try:
        # Try multiple locations for ffmpeg.bat
        ffmpeg_paths = [
            # First try the infrastructure/deployment directory
            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'infrastructure', 'deployment', 'ffmpeg.bat'),
            # Then try the old location
            os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'ffmpeg.bat'),
            # Finally try direct ffmpeg command
            'ffmpeg'
        ]

        ffmpeg_cmd = None
        for ffmpeg_path in ffmpeg_paths:
            if ffmpeg_path == 'ffmpeg':
                # Try direct ffmpeg command
                ffmpeg_cmd = ['ffmpeg']
                break
            elif os.path.exists(ffmpeg_path):
                ffmpeg_cmd = [ffmpeg_path]
                break

        if ffmpeg_cmd is None:
            ffmpeg_cmd = ['ffmpeg']

        # Add conversion parameters
        ffmpeg_cmd.extend([
            '-i', input_file,
            '-ar', '16000',  # 16kHz sample rate
            '-ac', '1',      # Mono
            '-y',            # Overwrite output file
            output_file
        ])

        # Execute conversion
        file_size_mb = os.path.getsize(input_file) / (1024 * 1024)
        send_progress("Converting", 0, f"Converting {file_ext.upper()} file ({file_size_mb:.2f} MB) to WAV format")
        process = subprocess.Popen(
            ffmpeg_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        # Wait for process to complete
        stdout, stderr = process.communicate()

        if process.returncode != 0:
            error_msg = stderr.decode()
            logging.error(f"FFmpeg conversion failed: {error_msg}")
            send_progress("Error", 0, f"FFmpeg conversion failed: {error_msg[:100]}...")
            raise Exception(f"FFmpeg conversion failed with code {process.returncode}")

        output_size_mb = os.path.getsize(output_file) / (1024 * 1024)
        send_progress("Converting", 100, f"Conversion complete: {file_ext.upper()} → WAV ({output_size_mb:.2f} MB)")
        return output_file

    except Exception as e:
        logging.error(f"Error converting file: {str(e)}")
        raise

def process_file(input_file, title=None, description=None, user_id=None):
    """Process input file through both pipelines"""
    start_time = time.time()
    temp_files = []

    try:
        # Create base output directory
        base_output_dir = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            'uploads',
            'processed'
        )

        # Create a unique identifier for this processing job
        if title:
            safe_title = "".join([c if c.isalnum() else "_" for c in title])
        else:
            safe_title = os.path.splitext(os.path.basename(input_file))[0]

        job_id = f"{safe_title}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # Create specific output directories as requested
        transcription_dir = os.path.join(base_output_dir, job_id, "transcription")
        minutes_dir = os.path.join(base_output_dir, job_id, "minutes of the meeting")

        # Create directories
        os.makedirs(transcription_dir, exist_ok=True)
        os.makedirs(minutes_dir, exist_ok=True)

        # 1. Convert to WAV if needed
        wav_file = convert_to_wav(input_file)
        if wav_file != input_file:
            temp_files.append(wav_file)

        # 2. Initialize Pipeline 1
        send_progress("Initializing", 10, "Loading speech recognition and transcription models")
        pipeline1 = AcademicTranscriptionPipeline()
        send_progress("Initializing", 15, "Models loaded successfully")

        # 3. Process through Pipeline 1 (transcription)
        send_progress("Transcribing", 20, "Starting audio transcription - this may take several minutes")
        transcript = pipeline1.create_translated_transcript(wav_file)

        # Count speakers and lines
        speakers = set()
        line_count = 0
        for line in transcript:
            line_count += 1
            if ":" in line:
                speaker = line.split(":", 1)[0].strip()
                if speaker and speaker != "Unknown":
                    speakers.add(speaker)

        # Clean transcript lines to remove problematic characters
        cleaned_transcript = []
        for line in transcript:
            # Replace any problematic characters
            cleaned_line = line.replace('\\', '/')
            # Remove any control characters
            cleaned_line = ''.join(ch for ch in cleaned_line if ord(ch) >= 32 or ch == '\n')
            # Replace any non-ASCII characters with their closest ASCII equivalent
            import unicodedata
            normalized_line = unicodedata.normalize('NFKD', cleaned_line)
            # Keep only ASCII characters
            ascii_line = ''.join(c for c in normalized_line if ord(c) < 128)
            cleaned_transcript.append(ascii_line)

        # Save transcript to file in transcription directory
        transcript_file = os.path.join(transcription_dir, "transcript.txt")
        with open(transcript_file, "w", encoding="utf-8") as f:
            for line in cleaned_transcript:
                f.write(line + "\n")

        # Check if we have the original Tagalog transcript
        original_tagalog_transcript = None
        original_tagalog_file = None
        if hasattr(pipeline1, 'original_transcript') and pipeline1.original_transcript:
            # Clean the original transcript
            cleaned_original = []
            for line in pipeline1.original_transcript:
                # Replace any problematic characters
                cleaned_line = line.replace('\\', '/')
                # Remove any control characters
                cleaned_line = ''.join(ch for ch in cleaned_line if ord(ch) >= 32 or ch == '\n')
                cleaned_original.append(cleaned_line)

            # Save original Tagalog transcript
            original_tagalog_file = os.path.join(transcription_dir, "transcript_tagalog.txt")
            with open(original_tagalog_file, "w", encoding="utf-8") as f:
                for line in cleaned_original:
                    f.write(line + "\n")

            # Store for response
            original_tagalog_transcript = "\n".join(cleaned_original)
            print(f"Saved original Tagalog transcript to {original_tagalog_file}")

        # Use the cleaned transcript for further processing
        transcript = cleaned_transcript

        send_progress("Transcribing", 60, f"Transcription complete: {line_count} lines, {len(speakers)} speakers identified")

        # 4. Process through Pipeline 2 (minutes generation)
        minutes = None
        minutes_file = None
        try:
            send_progress("Generating Minutes", 70, "Analyzing transcript content and identifying key topics")

            # Update progress during minutes generation
            def progress_callback(stage, progress):
                progress_value = 70 + int(progress * 0.3)  # Scale from 70% to 100%
                send_progress("Generating Minutes", progress_value, stage)

            # Set a timeout for minutes generation to avoid hanging
            import signal
            import platform

            # Windows doesn't support SIGALRM, so we'll use a different approach
            use_timer = platform.system() == 'Windows'

            if not use_timer:
                def timeout_handler(signum, frame):
                    raise TimeoutError("Minutes generation timed out after 5 minutes")

                # Set a 5-minute timeout on Unix/Linux systems
                signal.signal(signal.SIGALRM, timeout_handler)
                signal.alarm(300)  # 300 seconds = 5 minutes

            try:
                # Limit transcript length to avoid memory issues
                joined_transcript = "\n".join(transcript)
                if len(joined_transcript) > 50000:  # Limit to ~50K characters
                    logging.warning(f"Transcript too long ({len(joined_transcript)} chars), truncating to 50K chars")
                    joined_transcript = joined_transcript[:50000] + "\n\n[Transcript truncated due to length...]\n"

                minutes = process_meeting_transcript_enhanced(
                    joined_transcript,
                    include_executive_summary=True,
                    progress_callback=progress_callback
                )

                # Sanitize minutes content to ensure it's ASCII-compatible
                import unicodedata
                normalized_minutes = unicodedata.normalize('NFKD', minutes)
                # Keep only ASCII characters
                minutes = ''.join(c for c in normalized_minutes if ord(c) < 128)

                # Cancel the timeout if on Unix/Linux
                if not use_timer:
                    signal.alarm(0)

                # 5. Save minutes to file in minutes directory
                minutes_file = os.path.join(minutes_dir, "minutes.md")
                with open(minutes_file, "w", encoding="utf-8") as f:
                    f.write(minutes)

                # Count words in minutes
                word_count = len(minutes.split())

                send_progress("Generating Minutes", 100, f"Minutes generation complete: {word_count} words")
            except TimeoutError as te:
                logging.error(f"Timeout error in minutes generation: {str(te)}")
                send_progress("Generating Minutes", 100, f"Minutes generation timed out after 5 minutes")
                # Create a timeout error message as minutes
                error_message = "# Minutes Generation Timeout\n\nThe system took too long to generate minutes. This may be due to the length or complexity of the transcript.\n\n## Transcript\n\nPlease refer to the transcript file for the meeting content."
                minutes_file = os.path.join(minutes_dir, "minutes.md")
                with open(minutes_file, "w", encoding="utf-8") as f:
                    f.write(error_message)
                # Cancel the timeout if on Unix/Linux
                if not use_timer:
                    signal.alarm(0)
            except Exception as e:
                logging.error(f"Error in minutes generation: {str(e)}")
                logging.error(traceback.format_exc())
                send_progress("Generating Minutes", 100, f"Minutes generation failed: {str(e)}")
                # Create a simple error message as minutes
                error_message = f"# Minutes Generation Failed\n\nThe system was unable to generate minutes due to the following error:\n\n```\n{str(e)}\n```\n\n## Transcript\n\nPlease refer to the transcript file for the meeting content."
                minutes_file = os.path.join(minutes_dir, "minutes.md")
                with open(minutes_file, "w", encoding="utf-8") as f:
                    f.write(error_message)
                # Cancel the timeout if on Unix/Linux
                if not use_timer:
                    signal.alarm(0)
        except Exception as e:
            logging.error(f"Outer error in minutes generation: {str(e)}")
            logging.error(traceback.format_exc())
            send_progress("Generating Minutes", 100, f"Minutes generation failed: {str(e)}")
            # Create a simple error message as minutes
            error_message = f"# Minutes Generation Failed\n\nThe system was unable to generate minutes due to the following error:\n\n```\n{str(e)}\n```\n\n## Transcript\n\nPlease refer to the transcript file for the meeting content."
            minutes_file = os.path.join(minutes_dir, "minutes_error.md")
            with open(minutes_file, "w", encoding="utf-8") as f:
                f.write(error_message)

        # 6. Prepare response
        processing_time = time.time() - start_time

        # Extract speakers from transcript
        speakers = set()
        for line in transcript:
            if ":" in line:
                speaker = line.split(":", 1)[0].strip()
                if speaker and speaker != "Unknown":
                    speakers.add(speaker)

        # Ensure all data is JSON serializable
        # Convert transcript list to a single string to avoid potential issues
        transcript_text = "\n".join(transcript) if isinstance(transcript, list) else str(transcript)

        # Ensure speakers are serializable
        speaker_list = [str(s) for s in speakers]

        response = {
            "status": "success",
            "result": {
                "data": {
                    "transcript": transcript_text,
                    "transcript_file": transcript_file,
                    "minutes": minutes if minutes else "",
                    "minutes_file": minutes_file if minutes_file else "",
                    "speakers": speaker_list,
                    "duration": float(processing_time),
                    "transcription_dir": transcription_dir,
                    "minutes_dir": minutes_dir,
                    "job_id": job_id
                }
            }
        }

        # Add original Tagalog transcript if available
        if original_tagalog_file and os.path.exists(original_tagalog_file):
            response["result"]["data"]["original_transcript_file"] = original_tagalog_file

            # Read the content of the original transcript file
            try:
                with open(original_tagalog_file, "r", encoding="utf-8") as f:
                    original_tagalog_content = f.read()
                response["result"]["data"]["original_transcript"] = original_tagalog_content
            except Exception as e:
                logging.error(f"Error reading original Tagalog transcript: {str(e)}")
                # If we can't read the file, at least provide the path

        return response

    except Exception as e:
        logging.error(f"Processing error: {str(e)}")
        logging.error(traceback.format_exc())
        return {
            "status": "error",
            "error": str(e),
            "traceback": traceback.format_exc()
        }
    finally:
        # Clean up temporary files
        for file in temp_files:
            try:
                if os.path.exists(file):
                    os.remove(file)
            except Exception as e:
                logging.warning(f"Could not remove temp file {file}: {e}")

def main():
    """Main entry point for direct processing"""
    if len(sys.argv) < 2:
        print(json.dumps({
            "status": "error",
            "error": "No input file specified"
        }, ensure_ascii=True))
        return

    input_file = sys.argv[1]
    title = sys.argv[2] if len(sys.argv) > 2 else None
    description = sys.argv[3] if len(sys.argv) > 3 else None
    user_id = sys.argv[4] if len(sys.argv) > 4 else None

    if not os.path.exists(input_file):
        print(json.dumps({
            "status": "error",
            "error": f"Input file not found: {input_file}"
        }, ensure_ascii=True))
        return

    try:
        # Import the module that redirects stdout to stderr
        sys.path.append(os.path.join(os.path.dirname(__file__), 'Pipeline Phase1'))
        import PHASE1

        # Process the file
        result = process_file(input_file, title, description, user_id)

        # Restore original stdout before printing JSON result
        if hasattr(PHASE1, 'original_stdout'):
            sys.stdout = PHASE1.original_stdout

        # Ensure we're printing clean JSON without any other text
        # First, clear any previous output
        sys.stdout.flush()

        # Then print only the JSON result - use ensure_ascii=True to avoid encoding issues
        # Also sanitize any problematic characters in the result
        sanitized_result = sanitize_json_data(result)
        print(json.dumps(sanitized_result, ensure_ascii=True))
        sys.stdout.flush()
    except Exception as e:
        logging.error(f"Unhandled exception: {str(e)}")
        logging.error(traceback.format_exc())
        print(json.dumps({
            "status": "error",
            "error": str(e),
            "traceback": traceback.format_exc()
        }, ensure_ascii=True))

if __name__ == "__main__":
    main()
