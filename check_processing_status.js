const fetch = require('node-fetch');

async function checkProcessingStatus() {
    try {
        // Check if server is running
        const response = await fetch('http://localhost:3001/api/test');
        const data = await response.json();
        
        console.log('Server Status:', data);
        console.log('Server is running on port 3001');
        
        // You can add job ID here if you know it
        // const jobResponse = await fetch('http://localhost:3001/api/progress/YOUR_JOB_ID');
        
    } catch (error) {
        console.error('Error checking status:', error.message);
    }
}

checkProcessingStatus();
