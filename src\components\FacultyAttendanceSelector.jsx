import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';

const FacultyAttendanceSelector = ({ onAttendanceChange }) => {
  // Use a smaller list of faculty members to fit without scrolling
  const [facultyList, setFacultyList] = useState([
    // Initial faculty list
    { id: 1, name: '<PERSON> Villa<PERSON>', role: 'Dean', status: 'present' },
    { id: 2, name: '<PERSON>', role: 'Faculty', status: 'present' },
    { id: 3, name: '<PERSON><PERSON>', role: 'Faculty', status: 'present' },
    { id: 4, name: '<PERSON><PERSON>', role: 'Faculty', status: 'present' },
    { id: 5, name: '<PERSON>', role: 'Faculty', status: 'present' },
    { id: 6, name: '<PERSON>', role: 'Faculty', status: 'present' },
    { id: 7, name: '<PERSON>', role: 'Faculty', status: 'present' },
    { id: 8, name: '<PERSON>', role: 'Faculty', status: 'present' },
  ]);

  // Fetch faculty list from API when database integration is enabled
  // useEffect(() => {
  //   const fetchFaculty = async () => {
  //     try {
  //       const response = await fetch('http://localhost:3001/api/faculty');
  //       const data = await response.json();
  //       if (data.status === 'success') {
  //         // Add status field to each faculty member
  //         const facultyWithStatus = data.faculty.map(faculty => ({
  //           ...faculty,
  //           status: 'present'
  //         }));
  //         setFacultyList(facultyWithStatus);
  //       }
  //     } catch (error) {
  //       console.error('Error fetching faculty:', error);
  //     }
  //   };
  //
  //   fetchFaculty();
  // }, []);

  // Track if this is the initial render to prevent unnecessary calls
  const isInitialRender = useRef(true);

  // Memoize the attendance data to prevent unnecessary re-renders
  const attendanceData = useMemo(() => {
    return facultyList.map(faculty => ({
      facultyId: faculty.id,
      status: faculty.status
    }));
  }, [facultyList]);

  useEffect(() => {
    // Always notify parent component when attendance data changes
    // This ensures the parent has the initial data
    onAttendanceChange(attendanceData);

    // Mark that initial render is complete
    if (isInitialRender.current) {
      isInitialRender.current = false;
    }
  }, [attendanceData, onAttendanceChange]);

  // Track the last changed faculty ID for animation
  const [lastChanged, setLastChanged] = useState(null);

  // Memoize the status change handler to prevent unnecessary re-renders
  const handleStatusChange = useCallback((id, newStatus) => {
    console.log(`Changing status for faculty ID ${id} to ${newStatus}`);

    // Update the faculty list with the new status
    setFacultyList(prevList =>
      prevList.map(faculty =>
        faculty.id === id
          ? { ...faculty, status: newStatus }
          : faculty
      )
    );

    // Set the last changed faculty ID for animation
    setLastChanged(id);

    // Clear the animation after a delay
    setTimeout(() => {
      setLastChanged(null);
    }, 800);
  }, []);



  // State for managing the edit modal
  const [showEditModal, setShowEditModal] = useState(false);

  return (
    <div style={{
      width: '100%',
    }}>
      {/* Header with Edit button */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '15px'
      }}>
        <h3 style={{
          margin: 0,
          color: '#034FAF',
          fontSize: '16px',
          fontWeight: '600'
        }}>Faculty List</h3>
        <button
          onClick={() => setShowEditModal(true)}
          style={{
            padding: '5px 12px',
            background: '#034FAF',
            color: 'white',
            border: 'none',
            borderRadius: '16px',
            cursor: 'pointer',
            fontSize: '12px',
            fontWeight: 'bold',
            display: 'flex',
            alignItems: 'center',
            gap: '5px'
          }}
        >
          <span>✏️</span> Edit List
        </button>
      </div>

      <div style={{
        width: '100%',
      }}>
        <table style={{
          width: '100%',
          borderCollapse: 'collapse',
        }}>
          <thead style={{
            background: '#034FAF',
          }}>
            <tr style={{
              background: '#034FAF',
              color: 'white',
            }}>
              <th style={{ padding: '8px', textAlign: 'left' }}>Name</th>
              <th style={{ padding: '8px', textAlign: 'center' }}>Status</th>
              <th style={{ padding: '8px', textAlign: 'center' }}>Action</th>
            </tr>
          </thead>
          <tbody>
            {facultyList.map((faculty) => (
              <tr key={faculty.id} style={{
                borderBottom: '1px solid #eee',
                background: lastChanged === faculty.id ?
                  (faculty.status === 'present' ? 'rgba(220, 252, 231, 0.4)' : 'rgba(254, 226, 226, 0.4)') :
                  'transparent'
              }}>
                <td style={{ padding: '6px' }}>{faculty.name}</td>
                <td style={{ padding: '6px', textAlign: 'center' }}>
                  <span style={{
                    padding: '4px 8px',
                    borderRadius: '20px',
                    background: faculty.status === 'present' ? '#dcfce7' : '#fee2e2',
                    color: faculty.status === 'present' ? '#166534' : '#991b1b',
                    fontSize: '12px',
                    fontWeight: '600',
                    display: 'inline-block',
                    minWidth: '60px',
                    textAlign: 'center'
                  }}>
                    {faculty.status.charAt(0).toUpperCase() + faculty.status.slice(1)}
                  </span>
                </td>
                <td style={{ padding: '6px', textAlign: 'center' }}>
                  <button
                    onClick={() => {
                      console.log('Present button clicked for faculty ID:', faculty.id);
                      handleStatusChange(faculty.id, 'present');
                    }}
                    style={{
                      padding: '5px 10px',
                      background: faculty.status === 'present' ? '#166534' : '#90be6d',
                      color: 'white',
                      border: 'none',
                      borderRadius: '16px',
                      cursor: 'pointer',
                      fontSize: '12px',
                      marginRight: '5px',
                      fontWeight: 'bold',
                    }}
                  >
                    {faculty.status === 'present' ? '✓ Present' : 'Present'}
                  </button>
                  <button
                    onClick={() => {
                      console.log('Absent button clicked for faculty ID:', faculty.id);
                      handleStatusChange(faculty.id, 'absent');
                    }}
                    style={{
                      padding: '5px 10px',
                      background: faculty.status === 'absent' ? '#991b1b' : '#e5989b',
                      color: 'white',
                      border: 'none',
                      borderRadius: '16px',
                      cursor: 'pointer',
                      fontSize: '12px',
                      marginRight: '5px',
                      fontWeight: 'bold',
                    }}
                  >
                    {faculty.status === 'absent' ? '✓ Absent' : 'Absent'}
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>


      {/* Edit Modal */}
      {showEditModal && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '8px',
            padding: '20px',
            width: '80%',
            maxWidth: '600px',
            maxHeight: '80vh',
            overflowY: 'auto',
            boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '20px'
            }}>
              <h2 style={{ margin: 0, color: '#034FAF' }}>Edit Faculty List</h2>
              <button
                onClick={() => setShowEditModal(false)}
                style={{
                  background: 'none',
                  border: 'none',
                  fontSize: '20px',
                  cursor: 'pointer',
                  color: '#666'
                }}
              >
                ×
              </button>
            </div>

            {/* Faculty List Editor */}
            <FacultyListEditor
              facultyList={facultyList}
              setFacultyList={setFacultyList}
              onClose={() => setShowEditModal(false)}
            />
          </div>
        </div>
      )}
    </div>
  );
};

// Component for editing the faculty list
const FacultyListEditor = ({ facultyList, setFacultyList, onClose }) => {
  // State for the edited list
  const [editedList, setEditedList] = useState([...facultyList]);
  // State for the new faculty member
  const [newFaculty, setNewFaculty] = useState({ name: '', role: 'Faculty' });

  // Handle saving changes
  const handleSave = () => {
    setFacultyList(editedList);
    onClose();
  };

  // Handle adding a new faculty member
  const handleAddFaculty = () => {
    if (newFaculty.name.trim() === '') return;

    const newId = editedList.length > 0
      ? Math.max(...editedList.map(f => f.id)) + 1
      : 1;

    setEditedList([
      ...editedList,
      {
        id: newId,
        name: newFaculty.name,
        role: newFaculty.role,
        status: 'present'
      }
    ]);

    setNewFaculty({ name: '', role: 'Faculty' });
  };

  // Handle removing a faculty member
  const handleRemoveFaculty = (id) => {
    setEditedList(editedList.filter(faculty => faculty.id !== id));
  };

  // Handle editing a faculty member's name
  const handleEditName = (id, newName) => {
    setEditedList(editedList.map(faculty =>
      faculty.id === id ? { ...faculty, name: newName } : faculty
    ));
  };

  // Handle editing a faculty member's role
  const handleEditRole = (id, newRole) => {
    setEditedList(editedList.map(faculty =>
      faculty.id === id ? { ...faculty, role: newRole } : faculty
    ));
  };

  return (
    <div>
      {/* Add new faculty form */}
      <div style={{
        marginBottom: '20px',
        padding: '15px',
        backgroundColor: '#f8f9fa',
        borderRadius: '8px'
      }}>
        <h3 style={{ margin: '0 0 10px 0', fontSize: '16px', color: '#034FAF' }}>Add New Faculty</h3>
        <div style={{ display: 'flex', gap: '10px', marginBottom: '10px' }}>
          <input
            type="text"
            value={newFaculty.name}
            onChange={(e) => setNewFaculty({ ...newFaculty, name: e.target.value })}
            placeholder="Faculty Name"
            style={{
              flex: 3,
              padding: '8px',
              borderRadius: '4px',
              border: '1px solid #ddd'
            }}
          />
          <select
            value={newFaculty.role}
            onChange={(e) => setNewFaculty({ ...newFaculty, role: e.target.value })}
            style={{
              flex: 1,
              padding: '8px',
              borderRadius: '4px',
              border: '1px solid #ddd'
            }}
          >
            <option value="Dean">Dean</option>
            <option value="Faculty">Faculty</option>
          </select>
          <button
            onClick={handleAddFaculty}
            style={{
              padding: '8px 15px',
              backgroundColor: '#034FAF',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Add
          </button>
        </div>
      </div>

      {/* Faculty list */}
      <div style={{ marginBottom: '20px' }}>
        <h3 style={{ margin: '0 0 10px 0', fontSize: '16px', color: '#034FAF' }}>Current Faculty</h3>
        {editedList.length === 0 ? (
          <p style={{ color: '#666', fontStyle: 'italic' }}>No faculty members added yet.</p>
        ) : (
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr style={{ backgroundColor: '#f1f1f1' }}>
                <th style={{ padding: '8px', textAlign: 'left' }}>Name</th>
                <th style={{ padding: '8px', textAlign: 'left' }}>Role</th>
                <th style={{ padding: '8px', textAlign: 'center' }}>Actions</th>
              </tr>
            </thead>
            <tbody>
              {editedList.map((faculty) => (
                <tr key={faculty.id} style={{ borderBottom: '1px solid #eee' }}>
                  <td style={{ padding: '8px' }}>
                    <input
                      type="text"
                      value={faculty.name}
                      onChange={(e) => handleEditName(faculty.id, e.target.value)}
                      style={{
                        width: '100%',
                        padding: '6px',
                        borderRadius: '4px',
                        border: '1px solid #ddd'
                      }}
                    />
                  </td>
                  <td style={{ padding: '8px' }}>
                    <select
                      value={faculty.role}
                      onChange={(e) => handleEditRole(faculty.id, e.target.value)}
                      style={{
                        width: '100%',
                        padding: '6px',
                        borderRadius: '4px',
                        border: '1px solid #ddd'
                      }}
                    >
                      <option value="Dean">Dean</option>
                      <option value="Faculty">Faculty</option>
                    </select>
                  </td>
                  <td style={{ padding: '8px', textAlign: 'center' }}>
                    <button
                      onClick={() => handleRemoveFaculty(faculty.id)}
                      style={{
                        padding: '5px 10px',
                        backgroundColor: '#dc3545',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: 'pointer'
                      }}
                    >
                      Remove
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>

      {/* Action buttons */}
      <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '10px' }}>
        <button
          onClick={onClose}
          style={{
            padding: '8px 15px',
            backgroundColor: '#6c757d',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Cancel
        </button>
        <button
          onClick={handleSave}
          style={{
            padding: '8px 15px',
            backgroundColor: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Save Changes
        </button>
      </div>
    </div>
  );
};

export default FacultyAttendanceSelector;
