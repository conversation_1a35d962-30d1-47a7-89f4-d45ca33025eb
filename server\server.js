const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const multer = require('multer');
const { PythonShell } = require('python-shell');
const cookieParser = require('cookie-parser');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true
}));

app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));
app.use(cookieParser());

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const timestamp = Date.now();
    const originalName = file.originalname.replace(/[^a-zA-Z0-9.-]/g, '_');
    cb(null, `${timestamp}-${originalName}`);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 * 1024 // 5GB limit
  },
  fileFilter: function (req, file, cb) {
    // Additional validation to prevent malicious uploads
    if (!file.fieldname || file.fieldname.trim() === '') {
      return cb(new Error('Invalid field name'), false);
    }
    cb(null, true);
  }
});

// Basic test endpoint
app.get('/api/test', (req, res) => {
  res.json({
    status: 'success',
    message: 'Server is running',
    timestamp: new Date().toISOString()
  });
});

// List files endpoint
app.get('/api/list-files', (req, res) => {
  try {
    const requestedPath = req.query.path;
    if (!requestedPath) {
      return res.status(400).json({
        status: 'error',
        error: 'Path parameter is required'
      });
    }

    // Resolve the path relative to the project root
    let fullPath;
    if (path.isAbsolute(requestedPath)) {
      fullPath = requestedPath;
    } else {
      fullPath = path.join(__dirname, '..', requestedPath);
    }

    // Security check - ensure path is within project directory
    const projectRoot = path.resolve(__dirname, '..');
    const resolvedPath = path.resolve(fullPath);

    if (!resolvedPath.startsWith(projectRoot)) {
      return res.status(403).json({
        status: 'error',
        error: 'Access denied - path outside project directory'
      });
    }

    if (!fs.existsSync(resolvedPath)) {
      return res.status(404).json({
        status: 'error',
        error: 'Directory not found'
      });
    }

    const files = fs.readdirSync(resolvedPath);
    const fileDetails = files.map(file => {
      const filePath = path.join(resolvedPath, file);
      const stats = fs.statSync(filePath);

      return {
        name: file,
        path: filePath,
        size: stats.size,
        modified: stats.mtime.toISOString(),
        isDirectory: stats.isDirectory()
      };
    });

    res.json({
      status: 'success',
      path: resolvedPath,
      fileDetails: fileDetails
    });

  } catch (error) {
    console.error('Error listing files:', error);
    res.status(500).json({
      status: 'error',
      error: 'Failed to list files: ' + error.message
    });
  }
});

// Enhanced Multer error handler middleware
function handleMulterError(err, req, res, next) {
  if (err instanceof multer.MulterError) {
    console.error('Multer error:', err);

    switch (err.code) {
      case 'LIMIT_FILE_SIZE':
        return res.status(400).json({
          status: 'error',
          error: 'File size exceeds the 5GB limit'
        });
      case 'LIMIT_FILE_COUNT':
        return res.status(400).json({
          status: 'error',
          error: 'Too many files uploaded'
        });
      case 'LIMIT_UNEXPECTED_FILE':
        return res.status(400).json({
          status: 'error',
          error: 'Unexpected file field'
        });
      case 'MISSING_FIELD_NAME':
        return res.status(400).json({
          status: 'error',
          error: 'Missing or invalid field name in file upload'
        });
      default:
        return res.status(400).json({
          status: 'error',
          error: 'File upload error: ' + err.message
        });
    }
  }

  // Handle other file upload related errors
  if (err && err.message && err.message.includes('Invalid field name')) {
    return res.status(400).json({
      status: 'error',
      error: 'Invalid field name in file upload'
    });
  }

  // Pass other errors to the next error handler
  next(err);
}

// Store for tracking processing jobs
const processingJobs = new Map();

// File upload and processing endpoint
app.post('/api/process-audio', upload.single('file'), handleMulterError, async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        status: 'error',
        error: 'No file uploaded'
      });
    }

    const filePath = req.file.path;
    const fileName = req.file.filename;
    const jobId = Date.now().toString();

    console.log('Processing file:', fileName);
    console.log('File path:', filePath);

    // Get metadata from request
    const title = req.body.title || 'Meeting Recording';
    const description = req.body.description || '';
    const userId = req.body.userId || 'user';
    const meetingDate = req.body.meetingDate || new Date().toISOString();
    const facultyAttendance = req.body.facultyAttendance || '[]';

    console.log('Processing file with metadata:', {
      title,
      userId,
      meetingDate,
      facultyAttendance: facultyAttendance.substring(0, 100) + '...'
    });

    // Store job information
    processingJobs.set(jobId, {
      filePath,
      fileName,
      title,
      description,
      userId,
      meetingDate,
      facultyAttendance,
      status: 'started',
      progress: 0,
      stage: 'Initializing',
      details: 'Processing started',
      result: null,
      error: null
    });

    // Return initial JSON response with job ID
    res.json({
      status: 'success',
      jobId: jobId,
      message: 'Processing started'
    });

    // Start processing in background
    processFileInBackground(jobId);

  } catch (error) {
    console.error('Error processing audio:', error);
    res.status(500).json({
      status: 'error',
      error: 'Server error: ' + error.message
    });
  }
});

// Background processing function
async function processFileInBackground(jobId) {
  const job = processingJobs.get(jobId);
  if (!job) {
    console.error('Job not found:', jobId);
    return;
  }

  try {
    // Check if this is a text file
    const fileExtension = path.extname(job.fileName).toLowerCase();
    const isTextFile = ['.txt', '.md'].includes(fileExtension);

    if (isTextFile) {
      // Process text file
      await processTextFileBackground(jobId, job);
    } else {
      // Process audio/video file
      await processAudioVideoFileBackground(jobId, job);
    }
  } catch (error) {
    console.error('Background processing error:', error);
    job.status = 'error';
    job.error = error.message;
    processingJobs.set(jobId, job);
  }
}

// Process text file in background
async function processTextFileBackground(jobId, job) {
  const pythonScriptPath = path.join(__dirname, '../core/services/process_text_file.py');

  if (!fs.existsSync(pythonScriptPath)) {
    throw new Error('Text processing script not found');
  }

  job.stage = 'Processing Text';
  job.details = 'Processing text file...';
  job.progress = 25;
  processingJobs.set(jobId, job);

  const options = {
    mode: 'text',
    pythonPath: 'python',
    scriptPath: path.dirname(pythonScriptPath),
    args: [job.filePath]
  };

  const pyshell = new PythonShell('process_text_file.py', options);

  pyshell.on('message', function (message) {
    console.log('Python output:', message);
    job.progress = 75;
    job.details = message;
    processingJobs.set(jobId, job);
  });

  return new Promise((resolve, reject) => {
    pyshell.end(function (err) {
      if (err) {
        console.error('Text processing error:', err);
        job.status = 'error';
        job.error = 'Text processing failed: ' + err.message;
        processingJobs.set(jobId, job);
        reject(err);
      } else {
        job.status = 'complete';
        job.progress = 100;
        job.stage = 'Completed';
        job.details = 'Text processing completed successfully';
        job.result = {
          data: {
            transcript_file: job.filePath,
            minutes_file: job.filePath.replace('.txt', '_minutes.md'),
            transcript: 'Text file processed',
            minutes: 'Minutes generated from text file',
            speakers: [],
            transcription_dir: path.dirname(job.filePath),
            minutes_dir: path.dirname(job.filePath),
            job_id: jobId,
            title: job.title,
            description: job.description,
            user_id: job.userId
          }
        };
        processingJobs.set(jobId, job);
        resolve();
      }
    });
  });
}

// Process audio/video file in background
async function processAudioVideoFileBackground(jobId, job) {
  const pythonScriptPath = path.join(__dirname, '../core/services/run_gtx1050ti_processing.py');

  if (!fs.existsSync(pythonScriptPath)) {
    throw new Error('Audio processing script not found');
  }

  job.stage = 'Processing Audio/Video';
  job.details = 'Starting audio/video processing...';
  job.progress = 10;
  processingJobs.set(jobId, job);

  const options = {
    mode: 'text',
    pythonPath: 'python',
    scriptPath: path.dirname(pythonScriptPath),
    args: [job.filePath, '--title', job.title, '--description', job.description, '--mode', 'cpu'],
    timeout: 7200000, // 2 hours timeout (120 * 60 * 1000 ms)
    killSignal: 'SIGTERM',
    maxBuffer: 1024 * 1024 * 10 // 10MB buffer for output
  };

  const pyshell = new PythonShell('run_gtx1050ti_processing.py', options);
  let processingResult = null;
  let collectingJson = false;
  let jsonBuffer = '';

  pyshell.on('message', function (message) {
    console.log('Python processing output:', message);

    // Check for JSON output markers
    if (message.includes('===JSON_OUTPUT_START===')) {
      collectingJson = true;
      jsonBuffer = '';
      return;
    }

    if (message.includes('===JSON_OUTPUT_END===')) {
      collectingJson = false;
      if (jsonBuffer.trim()) {
        try {
          processingResult = JSON.parse(jsonBuffer);
          console.log('Successfully parsed JSON result:', processingResult.status);
        } catch (e) {
          console.error('Failed to parse JSON result:', e);
          console.error('JSON buffer:', jsonBuffer);
        }
      }
      return;
    }

    if (collectingJson) {
      jsonBuffer += message;
      return;
    }

    // Try to parse as direct JSON (fallback)
    try {
      const parsed = JSON.parse(message);
      if (parsed.status) {
        processingResult = parsed;
        console.log('Successfully parsed direct JSON result:', parsed.status);
      }
    } catch (e) {
      // Not JSON, treat as progress message
      // Update progress based on message content
      if (message.includes('Loading models') || message.includes('Loading')) {
        job.progress = 20;
        job.stage = 'Loading Models';
        job.details = 'Loading AI models (this may take 1-2 minutes)...';
      } else if (message.includes('Successfully loaded') || message.includes('Import successful')) {
        job.progress = 40;
        job.stage = 'Models Loaded';
        job.details = 'AI models loaded successfully, starting processing...';
      } else if (message.includes('Processing') || message.includes('Transcribing')) {
        job.progress = Math.min(job.progress + 5, 80);
        job.stage = 'Processing';
        job.details = message.length > 100 ? message.substring(0, 100) + '...' : message;
      } else {
        job.progress = Math.min(job.progress + 2, 85);
        job.details = message.length > 100 ? message.substring(0, 100) + '...' : message;
      }
      processingJobs.set(jobId, job);
    }
  });

  return new Promise((resolve, reject) => {
    // Set up a timeout handler
    const timeoutId = setTimeout(() => {
      console.log('Processing timeout reached, killing Python process');
      pyshell.kill('SIGTERM');
      job.status = 'error';
      job.error = 'Processing timed out after 2 hours';
      processingJobs.set(jobId, job);
      reject(new Error('Processing timeout'));
    }, 7200000); // 2 hours

    pyshell.end(function (err, code, signal) {
      clearTimeout(timeoutId); // Clear the timeout since process ended

      if (err) {
        console.error('Audio processing error:', err);
        console.error('Exit code:', code);
        console.error('Signal:', signal);

        let errorMessage = 'Audio processing failed: ' + err.message;
        if (signal === 'SIGTERM') {
          errorMessage = 'Processing was terminated (possibly due to timeout or memory issues)';
        } else if (code !== null && code !== 0) {
          errorMessage = `Processing failed with exit code ${code}: ${err.message}`;
        }

        job.status = 'error';
        job.error = errorMessage;
        processingJobs.set(jobId, job);
        reject(err);
      } else if (processingResult) {
        job.status = 'complete';
        job.progress = 100;
        job.stage = 'Completed';
        job.details = 'Processing completed successfully';
        job.result = processingResult;
        processingJobs.set(jobId, job);
        console.log('Audio processing completed successfully');
        resolve();
      } else {
        console.error('Processing completed but no JSON result was parsed');
        console.error('JSON buffer content:', jsonBuffer);
        job.status = 'error';
        job.error = 'Processing completed but no result received. Check Python script output for errors.';
        processingJobs.set(jobId, job);
        reject(new Error('No result received'));
      }
    });
  });
}

// Progress tracking endpoint for SSE
app.get('/api/progress/:jobId', (req, res) => {
  const jobId = req.params.jobId;
  const job = processingJobs.get(jobId);

  if (!job) {
    return res.status(404).json({
      status: 'error',
      error: 'Job not found'
    });
  }

  // Set up Server-Sent Events
  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': 'http://localhost:3000',
    'Access-Control-Allow-Credentials': 'true'
  });

  // Send initial connection message
  res.write(`data: ${JSON.stringify({
    type: 'connected',
    jobId: jobId,
    message: 'Connected to progress stream'
  })}\n\n`);

  // Function to send progress updates
  const sendProgress = () => {
    const currentJob = processingJobs.get(jobId);
    if (!currentJob) {
      res.end();
      return;
    }

    if (currentJob.status === 'error') {
      res.write(`data: ${JSON.stringify({
        type: 'error',
        error: currentJob.error || 'Processing failed'
      })}\n\n`);
      res.end();
      return;
    }

    if (currentJob.status === 'complete') {
      res.write(`data: ${JSON.stringify({
        type: 'complete',
        result: currentJob.result,
        progress: 100,
        stage: 'Completed',
        details: 'Processing completed successfully'
      })}\n\n`);
      res.end();
      return;
    }

    // Send progress update
    res.write(`data: ${JSON.stringify({
      type: 'progress',
      progress: currentJob.progress,
      stage: currentJob.stage,
      details: currentJob.details
    })}\n\n`);
  };

  // Send initial progress
  sendProgress();

  // Set up interval to send progress updates
  const progressInterval = setInterval(() => {
    const currentJob = processingJobs.get(jobId);
    if (!currentJob || currentJob.status === 'complete' || currentJob.status === 'error') {
      clearInterval(progressInterval);
      sendProgress(); // Send final update
      return;
    }
    sendProgress();
  }, 1000); // Send updates every second

  // Clean up when client disconnects
  req.on('close', () => {
    clearInterval(progressInterval);
    console.log(`Client disconnected from job ${jobId}`);
  });
});

// Text file processing endpoint
app.post('/api/process-text', upload.single('file'), handleMulterError, async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        status: 'error',
        error: 'No file uploaded'
      });
    }

    const filePath = req.file.path;
    const fileName = req.file.filename;

    console.log('Processing text file:', fileName);

    // Set up Server-Sent Events
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': 'http://localhost:3000',
      'Access-Control-Allow-Credentials': 'true'
    });

    const jobId = Date.now().toString();

    res.write(`data: ${JSON.stringify({
      type: 'connected',
      jobId: jobId,
      message: 'Text processing started'
    })}\n\n`);

    // Process text file with Python
    const pythonScriptPath = path.join(__dirname, '../python/process_text_file.py');

    if (!fs.existsSync(pythonScriptPath)) {
      res.write(`data: ${JSON.stringify({
        type: 'error',
        error: 'Text processing script not found'
      })}\n\n`);
      res.end();
      return;
    }

    const options = {
      mode: 'text',
      pythonPath: 'python',
      scriptPath: path.dirname(pythonScriptPath),
      args: [filePath]
    };

    const pyshell = new PythonShell('process_text_file.py', options);

    pyshell.on('message', function (message) {
      console.log('Python output:', message);
      res.write(`data: ${JSON.stringify({
        type: 'progress',
        message: message,
        progress: 50
      })}\n\n`);
    });

    pyshell.end(function (err, code, signal) {
      if (err) {
        console.error('Text processing error:', err);
        res.write(`data: ${JSON.stringify({
          type: 'error',
          error: 'Text processing failed: ' + err.message
        })}\n\n`);
      } else {
        res.write(`data: ${JSON.stringify({
          type: 'complete',
          message: 'Text processing completed successfully',
          progress: 100,
          fileName: fileName
        })}\n\n`);
      }
      res.end();
    });

  } catch (error) {
    console.error('Error processing text:', error);
    res.write(`data: ${JSON.stringify({
      type: 'error',
      error: 'Server error: ' + error.message
    })}\n\n`);
    res.end();
  }
});

// File content endpoint
app.get('/api/file', (req, res) => {
  try {
    const { path: filePath, type, fileId } = req.query;

    if (!filePath && !fileId) {
      return res.status(400).json({
        status: 'error',
        error: 'File path or file ID is required'
      });
    }

    let fullPath;

    if (filePath) {
      // Handle file path
      if (path.isAbsolute(filePath)) {
        fullPath = filePath;
      } else {
        fullPath = path.join(__dirname, '..', filePath);
      }
    } else if (fileId) {
      // Handle file ID - look for files with this ID in transcription directory
      const transcriptionDir = path.join(__dirname, '../python/transcription');
      const files = fs.readdirSync(transcriptionDir);

      const matchingFile = files.find(file =>
        file.startsWith(fileId) &&
        (type === 'tagalog' ? file.includes('_tagalog') : !file.includes('_tagalog'))
      );

      if (!matchingFile) {
        return res.status(404).json({
          status: 'error',
          error: 'File not found'
        });
      }

      fullPath = path.join(transcriptionDir, matchingFile);
    }

    // Security check
    const projectRoot = path.resolve(__dirname, '..');
    const resolvedPath = path.resolve(fullPath);

    if (!resolvedPath.startsWith(projectRoot)) {
      return res.status(403).json({
        status: 'error',
        error: 'Access denied'
      });
    }

    if (!fs.existsSync(resolvedPath)) {
      return res.status(404).json({
        status: 'error',
        error: 'File not found'
      });
    }

    const content = fs.readFileSync(resolvedPath, 'utf8');
    res.send(content);

  } catch (error) {
    console.error('Error reading file:', error);
    res.status(500).json({
      status: 'error',
      error: 'Failed to read file: ' + error.message
    });
  }
});

// Login endpoint
app.post('/api/login', (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({
        status: 'error',
        error: 'Username and password are required'
      });
    }

    // For demo purposes, accept any non-empty credentials
    // In a real app, you would validate against a database
    if (username.trim() && password.trim()) {
      const user = {
        id: Date.now().toString(),
        username: username,
        email: `${username}@example.com`,
        firstName: username,
        lastName: 'User'
      };

      // Set a session cookie
      res.cookie('session', 'demo-session-' + Date.now(), {
        httpOnly: true,
        secure: false, // Set to true in production with HTTPS
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
      });

      res.json({
        status: 'success',
        message: 'Login successful',
        user: user
      });
    } else {
      res.status(401).json({
        status: 'error',
        error: 'Invalid credentials'
      });
    }
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      status: 'error',
      error: 'Login failed: ' + error.message
    });
  }
});

// Register endpoint
app.post('/api/register', (req, res) => {
  try {
    const { username, password, email, firstName, lastName } = req.body;

    if (!username || !password || !email) {
      return res.status(400).json({
        status: 'error',
        error: 'Username, password, and email are required'
      });
    }

    // For demo purposes, accept any registration
    // In a real app, you would save to a database and check for duplicates
    const user = {
      id: Date.now().toString(),
      username: username,
      email: email,
      firstName: firstName || username,
      lastName: lastName || 'User'
    };

    res.json({
      status: 'success',
      message: 'Registration successful',
      user: user
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      status: 'error',
      error: 'Registration failed: ' + error.message
    });
  }
});

// CSRF token endpoint
app.get('/api/csrf-token', (req, res) => {
  // For now, return a dummy token since we're not implementing full CSRF protection
  res.json({
    csrfToken: 'dummy-csrf-token-' + Date.now()
  });
});

// Logout endpoint
app.post('/api/logout', (req, res) => {
  // Clear any session cookies
  res.clearCookie('session');
  res.json({
    status: 'success',
    message: 'Logged out successfully'
  });
});

// Minutes endpoint
app.post('/api/minutes', (req, res) => {
  try {
    const { userId, title, description, filePath, duration } = req.body;

    // For now, just return success - in a real app this would save to database
    res.json({
      status: 'success',
      message: 'Minute created successfully',
      id: Date.now().toString(),
      userId,
      title,
      description,
      filePath,
      duration
    });
  } catch (error) {
    console.error('Error creating minute:', error);
    res.status(500).json({
      status: 'error',
      error: 'Failed to create minute: ' + error.message
    });
  }
});

// Get minutes content endpoint
app.get('/api/minutes-content', (req, res) => {
  try {
    const { fileId, baseUrl } = req.query;

    if (!fileId) {
      return res.status(400).json({
        status: 'error',
        error: 'File ID is required'
      });
    }

    // Look for minutes file in the minutes directory
    const minutesDir = path.join(__dirname, '../python/minutes of the meeting');

    if (!fs.existsSync(minutesDir)) {
      return res.status(404).json({
        status: 'error',
        error: 'Minutes directory not found'
      });
    }

    const files = fs.readdirSync(minutesDir);
    const matchingFile = files.find(file =>
      file.startsWith(fileId) && file.endsWith('_minutes.md')
    );

    if (!matchingFile) {
      return res.status(404).json({
        status: 'error',
        error: 'Minutes file not found'
      });
    }

    const filePath = path.join(minutesDir, matchingFile);
    const content = fs.readFileSync(filePath, 'utf8');

    res.json({
      status: 'success',
      content: content,
      filePath: filePath
    });

  } catch (error) {
    console.error('Error reading minutes content:', error);
    res.status(500).json({
      status: 'error',
      error: 'Failed to read minutes content: ' + error.message
    });
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server error:', error);
  res.status(500).json({
    status: 'error',
    error: 'Internal server error'
  });
});

// Global error handler for any unhandled errors
app.use((err, req, res, next) => {
  console.error('Unhandled application error:', err);

  // Don't send error details in production
  const isDevelopment = process.env.NODE_ENV !== 'production';

  res.status(500).json({
    status: 'error',
    error: isDevelopment ? err.message : 'Internal server error',
    ...(isDevelopment && { stack: err.stack })
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Test the server at: http://localhost:${PORT}/api/test`);
});

module.exports = app;

