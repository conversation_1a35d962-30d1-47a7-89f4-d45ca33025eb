{"_num_labels": 3, "activation_dropout": 0.0, "activation_function": "gelu", "add_bias_logits": false, "add_final_layer_norm": false, "architectures": ["BartForConditionalGeneration"], "attention_dropout": 0.0, "bos_token_id": 0, "classif_dropout": 0.0, "classifier_dropout": 0.0, "d_model": 1024, "decoder_attention_heads": 16, "decoder_ffn_dim": 4096, "decoder_layerdrop": 0.0, "decoder_layers": 12, "decoder_start_token_id": 2, "dropout": 0.1, "early_stopping": null, "encoder_attention_heads": 16, "encoder_ffn_dim": 4096, "encoder_layerdrop": 0.0, "encoder_layers": 12, "eos_token_id": 2, "eos_token_ids": [2], "forced_eos_token_id": 2, "gradient_checkpointing": false, "id2label": {"0": "LABEL_0", "1": "LABEL_1", "2": "LABEL_2"}, "init_std": 0.02, "is_encoder_decoder": true, "label2id": {"LABEL_0": 0, "LABEL_1": 1, "LABEL_2": 2}, "max_length": null, "max_position_embeddings": 1024, "min_length": null, "model_type": "bart", "no_repeat_ngram_size": null, "normalize_before": false, "normalize_embedding": true, "num_beams": null, "num_hidden_layers": 12, "output_past": true, "pad_token_id": 1, "prefix": " ", "replacing_rate": 0, "scale_embedding": false, "static_position_embeddings": false, "student_decoder_layers": null, "student_encoder_layers": null, "task_specific_params": {}, "torch_dtype": "float32", "transformers_version": "4.50.3", "use_cache": true, "vocab_size": 50264}