#!/usr/bin/env python3
"""
Complete Processing Script
This script generates the final JSON output for a completed processing job.
"""

import json
import os
import sys
from datetime import datetime

def complete_processing(file_id, title="Meeting", description=""):
    """Generate completion JSON for a processing job."""
    
    # Define file paths
    transcript_file = f"C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\meeting-assistantv2\\python\\transcription\\{file_id}_transcript.txt"
    minutes_file = f"C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\meeting-assistantv2\\python\\minutes of the meeting\\{file_id}_minutes.md"
    tagalog_transcript_file = f"C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\meeting-assistantv2\\python\\transcription\\{file_id}_transcript_tagalog.txt"
    
    # Check if files exist
    transcript_exists = os.path.exists(transcript_file)
    minutes_exists = os.path.exists(minutes_file)
    tagalog_exists = os.path.exists(tagalog_transcript_file)
    
    # Count speakers from transcript if it exists
    speakers = []
    if transcript_exists:
        try:
            with open(transcript_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                speaker_set = set()
                for line in lines:
                    if ':' in line:
                        speaker = line.split(':', 1)[0].strip()
                        if speaker and speaker != 'Unknown':
                            speaker_set.add(speaker)
                speakers = sorted(list(speaker_set))
        except Exception as e:
            print(f"Error reading transcript: {e}")
    
    # Create the result JSON
    result = {
        "status": "success",
        "message": "Processing completed successfully",
        "data": {
            "transcript_file": transcript_file if transcript_exists else None,
            "minutes_file": minutes_file if minutes_exists else None,
            "tagalog_transcript_file": tagalog_transcript_file if tagalog_exists else None,
            "speakers": speakers,
            "title": title,
            "description": description,
            "processing_time": "Generated using fallback processing",
            "timestamp": datetime.now().isoformat()
        },
        "files_created": {
            "transcript": transcript_exists,
            "minutes": minutes_exists,
            "tagalog_transcript": tagalog_exists
        }
    }
    
    return result

def main():
    """Main function."""
    if len(sys.argv) < 2:
        print("Usage: python complete_processing.py <file_id> [title] [description]")
        return 1
    
    file_id = sys.argv[1]
    title = sys.argv[2] if len(sys.argv) > 2 else "Meeting"
    description = sys.argv[3] if len(sys.argv) > 3 else ""
    
    result = complete_processing(file_id, title, description)
    
    # Print the JSON result
    print(json.dumps(result, indent=2))
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
