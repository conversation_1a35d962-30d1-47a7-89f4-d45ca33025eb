export const sharedStyles = `
  /* Global styles to hide ALL scrollbars and prevent scrolling */
  html, body, #root, div {
    @apply m-0 p-0 overflow-hidden;
    -ms-overflow-style: none !important;  /* IE and Edge */
    scrollbar-width: none !important;  /* Firefox */
  }

  /* Hide webkit scrollbars */
  *::-webkit-scrollbar {
    @apply hidden;
    width: 0 !important;
    height: 0 !important;
  }

  /* Base styles */
  body {
    @apply font-montserrat antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Container styles */
  .page-container {
    @apply flex h-screen w-screen relative;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  }
`;

// Remove scrollableStyles since we're disabling all scrolling

